{"version": "V1.0", "name": "dstaff-mcp", "type": "DEFAULT", "base_image": [], "service": {"app": [{"name": "mcp-server-process-report", "version": "v1", "build": {"image": "mcp-server-process-report:v1", "externalPortList": [{"externalPort": 9001, "internalPort": 9000}], "values": {"replicas": 1, "expose": [9000], "env": [{"name": "MCP_PLATFORM_BASEURL", "value": "https://dstaff.dbappsecurity.com.cn"}, {"name": "MCP_AUTH_TOKEN", "value": ""}, {"name": "MCP_OPENAI__API_KEY", "value": "example"}, {"name": "MCP_OPENAI__BASE_URL", "value": "https://ai.gitee.com/v1"}, {"name": "MCP_OPENAI__MODEL", "value": "Qwen3-4B"}]}}}, {"name": "mcp-server-redteam", "version": "v1", "build": {"image": "mcp-server-redteam:v1", "externalPortList": [{"externalPort": 9000, "internalPort": 9000}], "values": {"replicas": 1, "expose": [9000], "volumes": [{"name": "redteam-config", "configMap": "redteam-config", "mountPath": "/app/config/.secrets.yaml", "subPath": ".secrets.yaml", "readOnly": true}]}}}], "common": [{"name": "lb-api", "cardinality": 1, "version": "v3.0.1-dev", "build": {"image": "k8s/lb-api:v3.0.1-dev"}}], "base": [{"name": "kubesphere", "version": "v3.4.1"}, {"name": "minio", "version": "RELEASE.2024-08-03T04-33-23Z"}], "extend": []}}